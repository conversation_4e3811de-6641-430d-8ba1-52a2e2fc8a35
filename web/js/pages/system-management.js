/**
 * 系统管理页面
 * 严格按照重构设计方案实现，使用公共工具模块减少冗余代码
 */

const SystemManagementPage = {
    // 页面配置
    config: {
        title: '系统管理',
        apiEndpoint: '/config/system'
    },

    // 当前配置数据
    currentConfig: null,

    /**
     * 渲染页面内容
     * @returns {string} HTML内容
     */
    render: function() {
        return `
            <div class="page-container">
                <div class="page-header">
                    <h2>${this.config.title}</h2>
                    <div class="page-actions">
                        <button type="button" class="btn btn-secondary" data-action="refresh">
                            <i class="icon-refresh"></i> 刷新
                        </button>
                    </div>
                </div>

                <div class="page-content">
                    <div class="card">
                        <div class="card-header">
                            <h3>系统基本设置</h3>
                            <p class="card-description">配置系统的基本运行参数</p>
                        </div>
                        
                        <div class="card-body">
                            <form id="system-config-form" class="config-form">
                                <div class="form-row">
                                    <div class="form-col">
                                        <label class="form-label required">系统名称</label>
                                        <input type="text" name="system_name" class="form-control" 
                                               placeholder="VICTEL IP交换机系统" maxlength="64" required>
                                        <small class="form-help">系统显示名称</small>
                                    </div>
                                    <div class="form-col">
                                        <label class="form-label">系统版本</label>
                                        <input type="text" name="system_version" class="form-control" 
                                               placeholder="v3.2.1" maxlength="16" readonly>
                                        <small class="form-help">当前系统版本（只读）</small>
                                    </div>
                                </div>

                                <div class="form-row">
                                    <div class="form-col">
                                        <label class="form-label">管理员邮箱</label>
                                        <input type="email" name="admin_email" class="form-control" 
                                               placeholder="<EMAIL>" maxlength="64">
                                        <small class="form-help">系统管理员邮箱地址</small>
                                    </div>
                                    <div class="form-col">
                                        <label class="form-label">时区设置</label>
                                        <select name="timezone" class="form-control">
                                            <option value="Asia/Shanghai">中国标准时间 (UTC+8)</option>
                                            <option value="UTC">协调世界时 (UTC+0)</option>
                                            <option value="America/New_York">美国东部时间 (UTC-5)</option>
                                            <option value="Europe/London">英国时间 (UTC+0)</option>
                                        </select>
                                        <small class="form-help">系统时区设置</small>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label class="form-label">日志级别</label>
                                    <select name="log_level" class="form-control">
                                        <option value="debug">调试 (DEBUG)</option>
                                        <option value="info">信息 (INFO)</option>
                                        <option value="warning">警告 (WARNING)</option>
                                        <option value="error">错误 (ERROR)</option>
                                    </select>
                                    <small class="form-help">系统日志记录级别</small>
                                </div>

                                <div class="form-group">
                                    <div class="checkbox-group">
                                        <label class="checkbox-label">
                                            <input type="checkbox" name="auto_backup" value="1">
                                            <span class="checkbox-text">启用自动备份</span>
                                        </label>
                                        <small class="form-help">定期自动备份系统配置</small>
                                    </div>
                                </div>

                                <div class="form-actions">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="icon-save"></i> 保存配置
                                    </button>
                                    <button type="button" class="btn btn-secondary" data-action="reset">
                                        <i class="icon-reset"></i> 重置
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-header">
                            <h3>系统操作</h3>
                            <p class="card-description">执行系统级别的管理操作</p>
                        </div>
                        
                        <div class="card-body">
                            <div class="action-grid">
                                <div class="action-item">
                                    <div class="action-icon">
                                        <i class="icon-restart"></i>
                                    </div>
                                    <div class="action-content">
                                        <h4>重启系统</h4>
                                        <p>重新启动整个系统服务</p>
                                        <button type="button" class="btn btn-warning" data-action="restart">
                                            <i class="icon-restart"></i> 重启系统
                                        </button>
                                    </div>
                                </div>

                                <div class="action-item">
                                    <div class="action-icon">
                                        <i class="icon-shutdown"></i>
                                    </div>
                                    <div class="action-content">
                                        <h4>关闭系统</h4>
                                        <p>安全关闭系统服务</p>
                                        <button type="button" class="btn btn-danger" data-action="shutdown">
                                            <i class="icon-shutdown"></i> 关闭系统
                                        </button>
                                    </div>
                                </div>

                                <div class="action-item">
                                    <div class="action-icon">
                                        <i class="icon-backup"></i>
                                    </div>
                                    <div class="action-content">
                                        <h4>备份配置</h4>
                                        <p>备份当前系统配置</p>
                                        <button type="button" class="btn btn-info" data-action="backup">
                                            <i class="icon-backup"></i> 立即备份
                                        </button>
                                    </div>
                                </div>

                                <div class="action-item">
                                    <div class="action-icon">
                                        <i class="icon-restore"></i>
                                    </div>
                                    <div class="action-content">
                                        <h4>恢复配置</h4>
                                        <p>从备份文件恢复配置</p>
                                        <input type="file" id="restore-file" accept=".bak,.tar.gz" style="display: none;">
                                        <button type="button" class="btn btn-secondary" data-action="restore">
                                            <i class="icon-restore"></i> 选择文件恢复
                                        </button>
                                    </div>
                                </div>

                                <div class="action-item">
                                    <div class="action-icon">
                                        <i class="icon-reset"></i>
                                    </div>
                                    <div class="action-content">
                                        <h4>恢复出厂设置</h4>
                                        <p>将系统恢复到出厂默认状态</p>
                                        <button type="button" class="btn btn-danger" data-action="factory-reset">
                                            <i class="icon-reset"></i> 恢复出厂设置
                                        </button>
                                    </div>
                                </div>

                                <div class="action-item">
                                    <div class="action-icon">
                                        <i class="icon-update"></i>
                                    </div>
                                    <div class="action-content">
                                        <h4>系统升级</h4>
                                        <p>升级系统到新版本</p>
                                        <input type="file" id="upgrade-file" accept=".bin,.img" style="display: none;">
                                        <button type="button" class="btn btn-primary" data-action="upgrade">
                                            <i class="icon-update"></i> 选择升级文件
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>


                </div>
            </div>
        `;
    },

    /**
     * 初始化页面
     */
    init: function() {
        this.bindEvents();
        this.loadConfig();
    },

    /**
     * 绑定事件
     */
    bindEvents: function() {
        const form = Utils.dom.find('#system-config-form');
        
        // 表单提交事件
        form.addEventListener('submit', (e) => {
            e.preventDefault();
            this.saveConfig();
        });

        // 重置按钮事件
        const resetBtn = Utils.dom.find('[data-action="reset"]');
        resetBtn.addEventListener('click', () => {
            this.resetForm();
        });

        // 刷新按钮事件
        const refreshBtn = Utils.dom.find('[data-action="refresh"]');
        refreshBtn.addEventListener('click', () => {
            this.loadConfig();
            this.updateSystemStatus();
        });

        // 系统操作按钮事件
        this.bindSystemActions();
    },

    /**
     * 绑定系统操作事件
     */
    bindSystemActions: function() {
        // 重启系统
        const restartBtn = Utils.dom.find('[data-action="restart"]');
        restartBtn.addEventListener('click', () => {
            this.confirmSystemAction('重启系统', '确定要重启系统吗？系统将暂时不可用。', () => {
                this.restartSystem();
            });
        });

        // 关闭系统
        const shutdownBtn = Utils.dom.find('[data-action="shutdown"]');
        shutdownBtn.addEventListener('click', () => {
            this.confirmSystemAction('关闭系统', '确定要关闭系统吗？系统将完全停止运行。', () => {
                this.shutdownSystem();
            });
        });

        // 备份配置
        const backupBtn = Utils.dom.find('[data-action="backup"]');
        backupBtn.addEventListener('click', () => {
            this.backupConfig();
        });

        // 恢复配置
        const restoreBtn = Utils.dom.find('[data-action="restore"]');
        const restoreFile = Utils.dom.find('#restore-file');
        restoreBtn.addEventListener('click', () => {
            restoreFile.click();
        });
        restoreFile.addEventListener('change', (e) => {
            if (e.target.files.length > 0) {
                this.restoreConfig(e.target.files[0]);
            }
        });

        // 恢复出厂设置
        const factoryResetBtn = Utils.dom.find('[data-action="factory-reset"]');
        factoryResetBtn.addEventListener('click', () => {
            this.confirmSystemAction('恢复出厂设置', '确定要恢复出厂设置吗？所有配置将被清除！', () => {
                this.factoryReset();
            });
        });

        // 系统升级
        const upgradeBtn = Utils.dom.find('[data-action="upgrade"]');
        const upgradeFile = Utils.dom.find('#upgrade-file');
        upgradeBtn.addEventListener('click', () => {
            upgradeFile.click();
        });
        upgradeFile.addEventListener('change', (e) => {
            if (e.target.files.length > 0) {
                this.upgradeSystem(e.target.files[0]);
            }
        });
    },

    /**
     * 确认系统操作
     * @param {string} title - 操作标题
     * @param {string} message - 确认消息
     * @param {Function} callback - 确认后的回调函数
     */
    confirmSystemAction: function(title, message, callback) {
        if (confirm(`${title}\n\n${message}`)) {
            callback();
        }
    },

    /**
     * 加载配置数据
     */
    loadConfig: async function() {
        try {
            UI.showLoading();
            const response = await API.system.getConfig();
            
            if (response && response.data) {
                this.currentConfig = response.data;
                this.populateForm(response.data);
            }
        } catch (error) {
            UI.showNotification('加载配置失败: ' + error.message, 'error');
        } finally {
            UI.hideLoading();
        }
    },

    /**
     * 填充表单数据
     * @param {Object} config - 配置数据
     */
    populateForm: function(config) {
        const fields = ['system_name', 'system_version', 'admin_email', 'timezone', 'log_level'];
        
        fields.forEach(field => {
            const input = Utils.dom.find(`[name="${field}"]`);
            if (input && config[field] !== undefined) {
                input.value = config[field];
            }
        });

        // 设置自动备份复选框
        const autoBackupCheckbox = Utils.dom.find('input[name="auto_backup"]');
        if (autoBackupCheckbox) {
            autoBackupCheckbox.checked = config.auto_backup === 1;
        }
    },

    /**
     * 保存配置
     */
    saveConfig: async function() {
        try {
            const formData = this.getFormData();
            
            // 验证表单数据
            if (!this.validateForm(formData)) {
                return;
            }

            UI.showLoading();
            const response = await API.system.saveConfig(formData);
            
            if (response && response.code === 200) {
                UI.showNotification('系统配置保存成功', 'success');
                this.currentConfig = formData;
            } else {
                throw new Error(response.message || '保存失败');
            }
        } catch (error) {
            UI.showNotification('保存配置失败: ' + error.message, 'error');
        } finally {
            UI.hideLoading();
        }
    },

    /**
     * 获取表单数据
     * @returns {Object} 表单数据
     */
    getFormData: function() {
        const form = Utils.dom.find('#system-config-form');
        const formData = new FormData(form);
        
        return {
            system_name: formData.get('system_name') || '',
            admin_email: formData.get('admin_email') || '',
            timezone: formData.get('timezone') || 'Asia/Shanghai',
            log_level: formData.get('log_level') || 'info',
            auto_backup: formData.get('auto_backup') ? 1 : 0
        };
    },

    /**
     * 验证表单数据
     * @param {Object} data - 表单数据
     * @returns {boolean} 验证结果
     */
    validateForm: function(data) {
        if (!data.system_name || data.system_name.trim() === '') {
            UI.showNotification('请输入系统名称', 'warning');
            return false;
        }

        if (data.admin_email && !Utils.validateEmail(data.admin_email)) {
            UI.showNotification('请输入有效的邮箱地址', 'warning');
            return false;
        }

        return true;
    },

    /**
     * 重置表单
     */
    resetForm: function() {
        if (this.currentConfig) {
            this.populateForm(this.currentConfig);
        } else {
            const form = Utils.dom.find('#system-config-form');
            form.reset();
        }
    },

    /**
     * 重启系统
     */
    restartSystem: async function() {
        try {
            UI.showLoading();
            const response = await API.system.restart();
            
            if (response && response.code === 200) {
                UI.showNotification('系统重启命令已发送，系统将在30秒后重启', 'success');
            } else {
                throw new Error(response.message || '重启失败');
            }
        } catch (error) {
            UI.showNotification('重启系统失败: ' + error.message, 'error');
        } finally {
            UI.hideLoading();
        }
    },

    /**
     * 关闭系统
     */
    shutdownSystem: async function() {
        try {
            UI.showLoading();
            const response = await API.system.shutdown();
            
            if (response && response.code === 200) {
                UI.showNotification('系统关闭命令已发送，系统将在30秒后关闭', 'success');
            } else {
                throw new Error(response.message || '关闭失败');
            }
        } catch (error) {
            UI.showNotification('关闭系统失败: ' + error.message, 'error');
        } finally {
            UI.hideLoading();
        }
    },

    /**
     * 备份配置
     */
    backupConfig: async function() {
        try {
            UI.showLoading();
            const response = await API.system.backup();
            
            if (response && response.code === 200) {
                UI.showNotification('配置备份成功', 'success');
                
                // 如果返回了下载链接，自动下载备份文件
                if (response.data && response.data.download_url) {
                    const link = document.createElement('a');
                    link.href = response.data.download_url;
                    link.download = response.data.filename || 'system_backup.tar.gz';
                    link.click();
                }
            } else {
                throw new Error(response.message || '备份失败');
            }
        } catch (error) {
            UI.showNotification('备份配置失败: ' + error.message, 'error');
        } finally {
            UI.hideLoading();
        }
    },

    /**
     * 恢复配置
     * @param {File} file - 备份文件
     */
    restoreConfig: async function(file) {
        try {
            if (!file) {
                UI.showNotification('请选择备份文件', 'warning');
                return;
            }

            UI.showLoading();
            const response = await API.system.restore(file);
            
            if (response && response.code === 200) {
                UI.showNotification('配置恢复成功，系统将重启以应用新配置', 'success');
            } else {
                throw new Error(response.message || '恢复失败');
            }
        } catch (error) {
            UI.showNotification('恢复配置失败: ' + error.message, 'error');
        } finally {
            UI.hideLoading();
        }
    },

    /**
     * 恢复出厂设置
     */
    factoryReset: async function() {
        try {
            UI.showLoading();
            const response = await API.system.factoryReset();
            
            if (response && response.code === 200) {
                UI.showNotification('恢复出厂设置成功，系统将重启', 'success');
            } else {
                throw new Error(response.message || '恢复出厂设置失败');
            }
        } catch (error) {
            UI.showNotification('恢复出厂设置失败: ' + error.message, 'error');
        } finally {
            UI.hideLoading();
        }
    },

    /**
     * 系统升级
     * @param {File} file - 升级文件
     */
    upgradeSystem: async function(file) {
        try {
            if (!file) {
                UI.showNotification('请选择升级文件', 'warning');
                return;
            }

            UI.showLoading();
            const response = await API.system.upgrade(file);
            
            if (response && response.code === 200) {
                UI.showNotification('系统升级开始，请勿断电或重启设备', 'success');
            } else {
                throw new Error(response.message || '升级失败');
            }
        } catch (error) {
            UI.showNotification('系统升级失败: ' + error.message, 'error');
        } finally {
            UI.hideLoading();
        }
    },

    /**
     * 更新系统状态
     */
    updateSystemStatus: async function() {
        try {
            // 这里可以调用系统状态API获取实时状态
            // 暂时使用模拟数据
            const statusData = {
                system_uptime: '15天 8小时 32分钟',
                cpu_usage: '25%',
                memory_usage: '68%',
                disk_usage: '42%',
                system_load: '0.85',
                network_connections: '156',
                active_processes: '89',
                last_backup: '2024-01-15 14:30:25'
            };

            this.updateStatusDisplay(statusData);
        } catch (error) {
            console.error('更新系统状态失败:', error);
        }
    },

    /**
     * 更新状态显示
     * @param {Object} statusData - 状态数据
     */
    updateStatusDisplay: function(statusData) {
        Object.keys(statusData).forEach(key => {
            const element = Utils.dom.find(`#${key.replace('_', '-')}`);
            if (element) {
                element.textContent = statusData[key];
            }
        });
    },

    /**
     * 开始状态监控
     */
    startStatusMonitoring: function() {
        // 每30秒更新一次系统状态
        setInterval(() => {
            this.updateSystemStatus();
        }, 30000);
    }
};
